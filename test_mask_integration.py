"""
mask功能集成测试脚本
验证mask.jpg是否正确集成到所有词云风格中
"""
import os
import sys
import logging
from datetime import datetime

# 导入系统模块
try:
    from wordcloud_generator import WordCloudGenerator
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保wordcloud_generator.py在当前目录中")
    sys.exit(1)

def test_mask_integration():
    """测试mask集成功能"""
    print("=" * 60)
    print("测试mask.jpg集成功能")
    print("=" * 60)
    
    # 检查mask.jpg是否存在
    mask_path = "mask.jpg"
    if not os.path.exists(mask_path):
        print(f"错误: mask.jpg文件不存在于当前目录")
        return False
    
    print(f"[成功] 找到mask.jpg文件")
    
    # 创建输出目录
    output_dir = "mask_test_output"
    os.makedirs(output_dir, exist_ok=True)
    
    # 测试关键词
    test_keywords = [
        ('人工智能', 25), ('机器学习', 20), ('深度学习', 18),
        ('自然语言处理', 15), ('计算机视觉', 12), ('数据科学', 10),
        ('大数据', 8), ('云计算', 7), ('物联网', 6), ('区块链', 5),
        ('Python', 15), ('算法', 12), ('模型', 10), ('系统', 8),
        ('应用', 6), ('技术', 5), ('开发', 4), ('分析', 4)
    ]
    
    # 创建词云生成器
    generator = WordCloudGenerator()
    
    # 获取所有默认风格
    styles = generator._get_default_styles()
    
    print(f"\n测试 {len(styles)} 种风格的mask集成:")
    
    results = {}
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    for style_name, style_config in styles.items():
        print(f"\n测试风格: {style_name}")
        print(f"  配置: {style_config}")
        
        # 检查是否包含mask_path
        if 'mask_path' in style_config:
            print(f"  [成功] 包含mask_path: {style_config['mask_path']}")
        else:
            print(f"  [失败] 缺少mask_path配置")
            results[style_name] = False
            continue
        
        # 检查是否包含contour设置
        if 'contour_width' in style_config and 'contour_color' in style_config:
            print(f"  [成功] 包含轮廓设置: 宽度={style_config['contour_width']}, 颜色={style_config['contour_color']}")
        else:
            print(f"  [失败] 缺少轮廓设置")
        
        # 生成词云
        filepath = os.path.join(output_dir, f"mask_test_{style_name}_{timestamp}.png")
        
        try:
            success = generator.generate_and_save(
                test_keywords,
                filepath,
                **style_config
            )
            
            if success:
                print(f"  [成功] 词云生成成功: {filepath}")
                results[style_name] = True
            else:
                print(f"  [失败] 词云生成失败")
                results[style_name] = False
                
        except Exception as e:
            print(f"  [失败] 生成过程中出现错误: {str(e)}")
            results[style_name] = False
    
    # 测试结果统计
    print("\n" + "=" * 60)
    print("测试结果统计")
    print("=" * 60)
    
    success_count = sum(1 for success in results.values() if success)
    total_count = len(results)
    
    print(f"总风格数: {total_count}")
    print(f"成功生成: {success_count}")
    print(f"失败数量: {total_count - success_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    
    print("\n各风格生成结果:")
    for style_name, success in results.items():
        status = "[成功]" if success else "[失败]"
        print(f"  {style_name}: {status}")
    
    # 检查生成的文件
    print(f"\n生成的文件:")
    if os.path.exists(output_dir):
        files = [f for f in os.listdir(output_dir) if f.startswith(f"mask_test_")]
        for file in files:
            filepath = os.path.join(output_dir, file)
            filesize = os.path.getsize(filepath)
            print(f"  - {file} ({filesize} bytes)")
    
    print(f"\n测试完成！输出文件位于: {output_dir}")
    return success_count == total_count

def test_mask_vs_nomask():
    """对比有mask和无mask的效果"""
    print("\n" + "=" * 60)
    print("对比测试: 有mask vs 无mask")
    print("=" * 60)
    
    # 测试关键词
    test_keywords = [
        ('Python', 15), ('编程', 12), ('开发', 10), ('数据', 8),
        ('人工智能', 7), ('机器学习', 6), ('Web', 6), ('框架', 5),
        ('库', 5), ('工具', 4), ('算法', 4), ('模型', 4)
    ]
    
    generator = WordCloudGenerator()
    
    # 创建输出目录
    output_dir = "mask_comparison_output"
    os.makedirs(output_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 无mask的配置
    nomask_config = {
        'background_color': 'white',
        'colormap': 'viridis'
    }
    
    # 有mask的配置
    mask_config = {
        'background_color': 'white',
        'colormap': 'viridis',
        'mask_path': 'mask.jpg',
        'contour_width': 1,
        'contour_color': 'steelblue'
    }
    
    # 生成无mask词云
    nomask_path = os.path.join(output_dir, f"comparison_nomask_{timestamp}.png")
    nomask_success = generator.generate_and_save(test_keywords, nomask_path, **nomask_config)
    
    # 生成有mask词云
    mask_path = os.path.join(output_dir, f"comparison_mask_{timestamp}.png")
    mask_success = generator.generate_and_save(test_keywords, mask_path, **mask_config)
    
    print(f"无mask词云: {'[成功]' if nomask_success else '[失败]'}")
    print(f"有mask词云: {'[成功]' if mask_success else '[失败]'}")
    
    if nomask_success and mask_success:
        print(f"\n对比文件:")
        print(f"  无mask: {nomask_path}")
        print(f"  有mask: {mask_path}")
        print("可以打开这两个文件对比视觉效果")
    
    return nomask_success and mask_success

def main():
    """主测试函数"""
    print("mask.jpg集成测试")
    print("=" * 60)
    
    # 设置日志级别
    logging.basicConfig(level=logging.INFO)
    
    try:
        # 测试mask集成
        test1_success = test_mask_integration()
        
        # 对比测试
        test2_success = test_mask_vs_nomask()
        
        print("\n" + "=" * 60)
        print("测试总结")
        print("=" * 60)
        
        if test1_success:
            print("[成功] mask集成测试通过")
        else:
            print("[失败] mask集成测试失败")
        
        if test2_success:
            print("[成功] mask对比测试通过")
        else:
            print("[失败] mask对比测试失败")
        
        if test1_success and test2_success:
            print("\n[完成] 所有测试通过！mask.jpg已成功集成到词云系统中")
        else:
            print("\n[错误] 部分测试失败，请检查配置和文件")
        
        return test1_success and test2_success
        
    except Exception as e:
        print(f"测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)