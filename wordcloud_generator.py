"""
词云生成和保存模块
负责根据关键词生成词云图片并保存
"""
import matplotlib.pyplot as plt
from wordcloud import WordCloud
import numpy as np
from PIL import Image
import random
from typing import List, Tuple, Dict, Optional, Union
import logging
import os

class WordCloudGenerator:
    """词云生成器类"""
    
    def __init__(self):
        # 设置matplotlib中文字体
        self._setup_chinese_font()
        
        # 默认颜色方案
        self.color_schemes = {
            'blue': ['#0066CC', '#0080FF', '#3399FF', '#66B2FF', '#99CCFF'],
            'green': ['#009900', '#00CC00', '#33FF33', '#66FF66', '#99FF99'],
            'red': ['#CC0000', '#FF0000', '#FF3333', '#FF6666', '#FF9999'],
            'purple': ['#6600CC', '#8000FF', '#9933FF', '#B266FF', '#CC99FF'],
            'orange': ['#FF6600', '#FF8000', '#FF9933', '#FFB266', '#FFCC99'],
            'rainbow': ['#FF0000', '#FF7F00', '#FFFF00', '#00FF00', '#0000FF', '#4B0082', '#9400D3']
        }
    
    def _setup_chinese_font(self):
        """设置中文字体"""
        try:
            # 尝试设置中文字体
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False
        except Exception as e:
            logging.warning(f"设置中文字体失败: {str(e)}")
    
    def generate_wordcloud(self, 
                          keywords: List[Tuple[str, int]], 
                          width: int = 800, 
                          height: int = 600,
                          max_words: int = 100,
                          background_color: str = 'white',
                          colormap: str = 'viridis',
                          color_scheme: str = None,
                          mask_path: str = None,
                          contour_width: int = 0,
                          contour_color: str = 'steelblue',
                          relative_scaling: float = 0.5,
                          random_state: int = 42) -> Optional[WordCloud]:
        """
        生成词云对象
        
        Args:
            keywords: 关键词和词频列表
            width: 图片宽度
            height: 图片高度
            max_words: 最大词数
            background_color: 背景颜色
            colormap: 颜色映射
            color_scheme: 颜色方案
            mask_path: 遮罩图片路径
            contour_width: 轮廓宽度
            contour_color: 轮廓颜色
            relative_scaling: 相对缩放比例
            random_state: 随机种子
            
        Returns:
            WordCloud对象，生成失败返回None
        """
        try:
            # 准备词频字典
            word_freq = dict(keywords)
            
            # 准备遮罩
            mask = None
            if mask_path and os.path.exists(mask_path):
                try:
                    mask = np.array(Image.open(mask_path))
                    logging.info(f"使用遮罩图片: {mask_path}")
                except Exception as e:
                    logging.warning(f"加载遮罩图片失败: {str(e)}")
            
            # 创建颜色函数
            color_func = None
            if color_scheme and color_scheme in self.color_schemes:
                color_func = self._create_color_function(color_scheme)
            
            # 创建词云对象
            wordcloud = WordCloud(
                width=width,
                height=height,
                max_words=max_words,
                background_color=background_color,
                colormap=colormap if color_func is None else None,
                mask=mask,
                contour_width=contour_width,
                contour_color=contour_color,
                relative_scaling=relative_scaling,
                random_state=random_state,
                color_func=color_func,
                font_path='simhei.ttf'  # 使用中文字体
            )
            
            # 生成词云
            wordcloud.generate_from_frequencies(word_freq)
            
            return wordcloud
            
        except Exception as e:
            logging.error(f"生成词云失败: {str(e)}")
            return None
    
    def _create_color_function(self, color_scheme: str):
        """
        创建颜色函数
        
        Args:
            color_scheme: 颜色方案名称
            
        Returns:
            颜色函数
        """
        colors = self.color_schemes.get(color_scheme, self.color_schemes['blue'])
        
        def color_func(word, font_size, position, orientation, random_state=None, **kwargs):
            return random.choice(colors)
        
        return color_func
    
    def save_wordcloud(self, 
                      wordcloud: WordCloud, 
                      filepath: str,
                      dpi: int = 300,
                      bbox_inches: str = 'tight',
                      pad_inches: float = 0.1) -> bool:
        """
        保存词云图片
        
        Args:
            wordcloud: WordCloud对象
            filepath: 保存路径
            dpi: 图片分辨率
            bbox_inches: 边界框
            pad_inches: 边距
            
        Returns:
            是否保存成功
        """
        try:
            # 创建图形
            plt.figure(figsize=(10, 8))
            plt.imshow(wordcloud, interpolation='bilinear')
            plt.axis('off')
            plt.tight_layout(pad=pad_inches)
            
            # 确保目录存在
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            
            # 保存图片
            plt.savefig(filepath, dpi=dpi, bbox_inches=bbox_inches, 
                       facecolor=wordcloud.background_color)
            plt.close()
            
            logging.info(f"词云已保存到: {filepath}")
            return True
            
        except Exception as e:
            logging.error(f"保存词云失败: {str(e)}")
            return False
    
    def generate_and_save(self, 
                          keywords: List[Tuple[str, int]], 
                          filepath: str,
                          **kwargs) -> bool:
        """
        生成并保存词云
        
        Args:
            keywords: 关键词和词频列表
            filepath: 保存路径
            **kwargs: 其他参数
            
        Returns:
            是否成功
        """
        try:
            # 生成词云
            wordcloud = self.generate_wordcloud(keywords, **kwargs)
            
            if wordcloud is None:
                return False
            
            # 保存词云
            return self.save_wordcloud(wordcloud, filepath)
            
        except Exception as e:
            logging.error(f"生成并保存词云失败: {str(e)}")
            return False
    
    def generate_multiple_styles(self, 
                                keywords: List[Tuple[str, int]], 
                                base_path: str,
                                styles: List[Dict] = None) -> Dict[str, bool]:
        """
        生成多种风格的词云
        
        Args:
            keywords: 关键词和词频列表
            base_path: 基础路径
            styles: 风格配置列表
            
        Returns:
            各风格生成结果
        """
        if styles is None:
            styles = self._get_default_styles()
        
        results = {}
        
        for style_name, style_config in styles.items():
            filepath = f"{base_path}_{style_name}.png"
            
            success = self.generate_and_save(
                keywords, 
                filepath, 
                **style_config
            )
            
            results[style_name] = success
        
        return results
    
    def _get_default_styles(self) -> Dict[str, Dict]:
        """
        获取默认风格配置
        
        Returns:
            风格配置字典
        """
        return {
            'default': {
                'background_color': 'white',
                'colormap': 'viridis'
            },
            'dark': {
                'background_color': 'black',
                'colormap': 'plasma'
            },
            'blue_theme': {
                'background_color': 'white',
                'color_scheme': 'blue'
            },
            'green_theme': {
                'background_color': 'white',
                'color_scheme': 'green'
            },
            'red_theme': {
                'background_color': 'white',
                'color_scheme': 'red'
            },
            'rainbow': {
                'background_color': 'white',
                'color_scheme': 'rainbow'
            }
        }
    
    def get_wordcloud_info(self, wordcloud: WordCloud) -> Dict[str, any]:
        """
        获取词云信息
        
        Args:
            wordcloud: WordCloud对象
            
        Returns:
            词云信息
        """
        return {
            'width': wordcloud.width,
            'height': wordcloud.height,
            'max_words': wordcloud.max_words,
            'background_color': wordcloud.background_color,
            'colormap': wordcloud.colormap,
            'relative_scaling': wordcloud.relative_scaling
        }
    
    def create_comparison_chart(self, 
                               keywords_list: List[List[Tuple[str, int]]], 
                               titles: List[str],
                               filepath: str,
                               rows: int = 2,
                               cols: int = 2) -> bool:
        """
        创建对比词云图
        
        Args:
            keywords_list: 关键词列表的列表
            titles: 标题列表
            filepath: 保存路径
            rows: 行数
            cols: 列数
            
        Returns:
            是否成功
        """
        try:
            fig, axes = plt.subplots(rows, cols, figsize=(15, 12))
            axes = axes.flatten()
            
            for i, (keywords, title) in enumerate(zip(keywords_list, titles)):
                if i >= len(axes):
                    break
                
                wordcloud = self.generate_wordcloud(keywords)
                if wordcloud:
                    axes[i].imshow(wordcloud, interpolation='bilinear')
                    axes[i].set_title(title, fontsize=14, fontweight='bold')
                    axes[i].axis('off')
            
            # 隐藏多余的子图
            for i in range(len(keywords_list), len(axes)):
                axes[i].axis('off')
            
            plt.tight_layout()
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()
            
            logging.info(f"对比词云图已保存到: {filepath}")
            return True
            
        except Exception as e:
            logging.error(f"创建对比词云图失败: {str(e)}")
            return False