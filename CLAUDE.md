# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a word cloud generation project. The repository is currently empty and ready for initial development setup.

## Development Setup

Since this is a new project, you'll need to initialize the development environment:

```bash
# Initialize npm project
npm init -y

# Install common dependencies for word cloud projects
npm install react react-dom
npm install --save-dev vite @vitejs/plugin-react
npm install d3 d3-cloud
npm install typescript @types/react @types/d3
```

## Project Structure (Recommended)

Based on the project name `wordCloud`, the recommended structure would be:

```
src/
├── components/
│   ├── WordCloud.tsx      # Main word cloud component
│   ├── TextInput.tsx      # Text input component
│   └── Controls.tsx       # Settings controls
├── utils/
│   ├── textProcessing.ts  # Text analysis and word extraction
│   └── cloudGenerator.ts  # D3-cloud integration
├── types/
│   └── index.ts          # TypeScript type definitions
├── App.tsx               # Main application component
├── main.tsx              # Entry point
└── styles/
    └── global.css        # Global styles
```

## Common Development Commands

```bash
# Development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Type checking
npm run type-check

# Linting
npm run lint
```

## Key Technical Considerations

- **Text Processing**: Implement efficient word extraction, filtering, and frequency counting
- **Performance**: Optimize for large text inputs and smooth animations
- **Responsiveness**: Ensure word cloud renders well on different screen sizes
- **Customization**: Allow users to configure colors, fonts, layouts, and word filtering
- **Accessibility**: Provide proper ARIA labels and keyboard navigation