"""
关键词过滤功能模块
提供更精细的关键词过滤和自定义规则
"""
import re
from typing import List, Tuple, Set, Dict, Callable
import logging

class KeywordFilter:
    """关键词过滤器类"""
    
    def __init__(self):
        # 加载默认过滤规则
        self.filter_rules = self._load_default_rules()
        
        # 自定义过滤函数列表
        self.custom_filters = []
        
    def _load_default_rules(self) -> Dict[str, Set[str]]:
        """
        加载默认过滤规则
        
        Returns:
            过滤规则字典
        """
        rules = {
            # 常见无意义词汇
            'common_words': {
                '可以', '能够', '应该', '需要', '必须', '要', '想', '希望', '觉得',
                '认为', '感觉', '发现', '知道', '了解', '明白', '理解', '记得', '忘记',
                '看见', '听到', '听说', '想到', '遇到', '碰到', '发生', '出现', '存在',
                '属于', '成为', '变成', '当作', '看作', '开始', '结束', '继续', '停止',
                '进行', '开展', '实施', '执行', '操作', '处理', '解决', '面对', '接受'
            },
            
            # 情感词汇
            'emotion_words': {
                '高兴', '快乐', '开心', '难过', '伤心', '痛苦', '兴奋', '激动', '平静',
                '喜欢', '讨厌', '爱', '恨', '满意', '失望', '惊讶', '困惑', '担忧',
                '愤怒', '害怕', '紧张', '放松', '疲惫', '精力充沛', '无聊', '有趣'
            },
            
            # 程度副词
            'degree_words': {
                '非常', '特别', '十分', '相当', '比较', '稍微', '有点', '有些', '多少',
                '很', '太', '最', '更', '越', '较', '稍', '略', '颇', '甚', '极', '顶'
            },
            
            # 时间词汇
            'time_words': {
                '现在', '过去', '将来', '今天', '明天', '昨天', '前天', '后天',
                '上午', '下午', '晚上', '早上', '中午', '夜里', '凌晨', '半夜',
                '今年', '去年', '明年', '本月', '上月', '下月', '本周', '上周', '下周'
            },
            
            # 空间词汇
            'space_words': {
                '这里', '那里', '上面', '下面', '里面', '外面', '前面', '后面',
                '左边', '右边', '中间', '旁边', '附近', '远处', '近处', '上方', '下方'
            },
            
            # 数字和量词
            'number_words': {
                '一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '百', '千',
                '万', '亿', '零', '半', '几', '两', '双', '对', '副', '群', '批', '套',
                '个', '只', '条', '张', '件', '本', '部', '台', '架', '辆', '艘'
            },
            
            # 指示代词
            'pronoun_words': {
                '这', '那', '这个', '那个', '这些', '那些', '这样', '那样', '这么',
                '那么', '什么', '怎么', '怎样', '哪里', '谁', '为什么', '如何'
            },
            
            # 连接词
            'conjunction_words': {
                '和', '与', '及', '以及', '或', '或者', '还是', '但是', '可是',
                '然而', '不过', '只是', '因为', '所以', '由于', '因此', '于是',
                '而且', '并且', '另外', '此外', '除了', '关于', '对于', '至于'
            }
        }
        
        return rules
    
    def add_custom_filter(self, filter_func: Callable[[str], bool]) -> None:
        """
        添加自定义过滤函数
        
        Args:
            filter_func: 过滤函数，接受词汇返回布尔值
        """
        self.custom_filters.append(filter_func)
    
    def add_custom_words(self, words: List[str], category: str = 'custom') -> None:
        """
        添加自定义过滤词汇
        
        Args:
            words: 词汇列表
            category: 分类名称
        """
        if category not in self.filter_rules:
            self.filter_rules[category] = set()
        
        self.filter_rules[category].update(words)
    
    def filter_by_length(self, word: str, min_len: int = 2, max_len: int = 10) -> bool:
        """
        按长度过滤
        
        Args:
            word: 词汇
            min_len: 最小长度
            max_len: 最大长度
            
        Returns:
            是否通过过滤
        """
        return min_len <= len(word) <= max_len
    
    def filter_by_pattern(self, word: str, patterns: List[str]) -> bool:
        """
        按正则表达式模式过滤
        
        Args:
            word: 词汇
            patterns: 正则表达式列表
            
        Returns:
            是否通过过滤
        """
        for pattern in patterns:
            if re.match(pattern, word):
                return False
        return True
    
    def filter_by_category(self, word: str, categories: List[str]) -> bool:
        """
        按分类过滤
        
        Args:
            word: 词汇
            categories: 要过滤的分类列表
            
        Returns:
            是否通过过滤
        """
        for category in categories:
            if category in self.filter_rules and word in self.filter_rules[category]:
                return False
        return True
    
    def apply_custom_filters(self, word: str) -> bool:
        """
        应用自定义过滤函数
        
        Args:
            word: 词汇
            
        Returns:
            是否通过过滤
        """
        for filter_func in self.custom_filters:
            if not filter_func(word):
                return False
        return True
    
    def filter_keywords(self, keywords: List[Tuple[str, int]], 
                       min_freq: int = 2,
                       min_len: int = 2,
                       max_len: int = 10,
                       exclude_categories: List[str] = None,
                       exclude_patterns: List[str] = None) -> List[Tuple[str, int]]:
        """
        综合过滤关键词
        
        Args:
            keywords: 关键词列表
            min_freq: 最小词频
            min_len: 最小长度
            max_len: 最大长度
            exclude_categories: 排除的分类
            exclude_patterns: 排除的正则模式
            
        Returns:
            过滤后的关键词列表
        """
        if exclude_categories is None:
            exclude_categories = ['common_words', 'emotion_words', 'degree_words']
        
        if exclude_patterns is None:
            exclude_patterns = [r'^\d+$', r'^[a-zA-Z]+$', r'^[^\w\u4e00-\u9fff]+$']
        
        filtered = []
        
        for word, freq in keywords:
            # 检查词频
            if freq < min_freq:
                continue
            
            # 检查长度
            if not self.filter_by_length(word, min_len, max_len):
                continue
            
            # 检查分类
            if not self.filter_by_category(word, exclude_categories):
                continue
            
            # 检查正则模式
            if not self.filter_by_pattern(word, exclude_patterns):
                continue
            
            # 应用自定义过滤
            if not self.apply_custom_filters(word):
                continue
            
            filtered.append((word, freq))
        
        return filtered
    
    def get_filter_stats(self, original_count: int, filtered_count: int) -> Dict[str, any]:
        """
        获取过滤统计信息
        
        Args:
            original_count: 原始词汇数量
            filtered_count: 过滤后词汇数量
            
        Returns:
            统计信息
        """
        return {
            'original_count': original_count,
            'filtered_count': filtered_count,
            'removed_count': original_count - filtered_count,
            'retention_rate': filtered_count / original_count if original_count > 0 else 0,
            'filter_categories': list(self.filter_rules.keys()),
            'custom_filters_count': len(self.custom_filters)
        }
    
    def export_filter_rules(self, filepath: str) -> None:
        """
        导出过滤规则到文件
        
        Args:
            filepath: 文件路径
        """
        try:
            import json
            
            # 将set转换为list以便JSON序列化
            export_data = {}
            for category, words in self.filter_rules.items():
                export_data[category] = list(words)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            logging.info(f"过滤规则已导出到: {filepath}")
            
        except Exception as e:
            logging.error(f"导出过滤规则失败: {str(e)}")
    
    def import_filter_rules(self, filepath: str) -> None:
        """
        从文件导入过滤规则
        
        Args:
            filepath: 文件路径
        """
        try:
            import json
            
            with open(filepath, 'r', encoding='utf-8') as f:
                import_data = json.load(f)
            
            # 更新过滤规则
            for category, words in import_data.items():
                self.filter_rules[category] = set(words)
            
            logging.info(f"过滤规则已从文件导入: {filepath}")
            
        except Exception as e:
            logging.error(f"导入过滤规则失败: {str(e)}")