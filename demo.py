"""
词云生成系统演示脚本
展示系统的各种功能和使用方法
"""
import os
import sys
from datetime import datetime

# 导入系统模块
from main import WordCloudSystem

def demo_text_processing():
    """演示文本处理功能"""
    print("=" * 60)
    print("演示1: 文本处理功能")
    print("=" * 60)
    
    # 演示文本
    demo_text = """
    Python是一种高级编程语言，由Guido van Rossum于1991年创建。
    它具有简洁易读的语法，强大的标准库，以及丰富的第三方包。
    Python支持多种编程范式，包括面向对象、命令式、函数式和过程式编程。
    它在Web开发、数据分析、人工智能、科学计算等领域有广泛应用。
    Django和Flask是流行的Python Web框架，NumPy和Pandas用于数据分析，
    TensorFlow和PyTorch用于机器学习，Matplotlib和Seaborn用于数据可视化。
    Python的简单性和可读性使其成为初学者的理想选择，
    同时其强大的功能也满足了专业开发者的需求。
    """
    
    # 创建系统实例
    system = WordCloudSystem({
        'output_dir': 'demo_output',
        'log_level': 'INFO'
    })
    
    # 处理文本
    result = system.process_text(demo_text, {
        'top_k': 30,
        'min_freq': 1,
        'min_len': 2,
        'max_len': 10,
        'styles': ['default', 'blue_theme', 'green_theme'],
        'include_chart': True,
        'include_stats': True,
        'export_filter_details': True
    })
    
    if result['success']:
        print("文本处理成功!")
        print(f"原始关键词数量: {result['original_keywords_count']}")
        print(f"过滤后关键词数量: {result['filtered_keywords_count']}")
        print("前10个关键词:")
        for i, (word, freq) in enumerate(result['top_keywords'], 1):
            print(f"  {i:2d}. {word}: {freq}")
        
        print(f"\n生成的文件:")
        print(f"  词云图片: {result['wordcloud_file']}")
        print(f"  Excel文件: {result['excel_file']}")
    else:
        print(f"文本处理失败: {result['error']}")

def demo_keyword_filtering():
    """演示关键词过滤功能"""
    print("\n" + "=" * 60)
    print("演示2: 关键词过滤功能")
    print("=" * 60)
    
    from text_processor import TextProcessor
    from keyword_filter import KeywordFilter
    
    # 测试文本
    test_text = """
    我们可以看到这个非常重要的问题需要解决，应该要仔细考虑。
    这个方法非常好，可以帮助我们更好地理解和处理。
    我认为这个解决方案是正确的，能够有效地解决问题。
    我们需要进行更多的测试，以确保系统的稳定性和可靠性。
    这个技术非常先进，具有很大的潜力和发展前景。
    我们希望能够得到更好的结果，提高工作效率和质量。
    """
    
    # 初始化处理器
    processor = TextProcessor()
    keyword_filter = KeywordFilter()
    
    # 提取关键词
    keywords = processor.extract_keywords(test_text, top_k=50)
    print(f"原始关键词数量: {len(keywords)}")
    print("原始前10个关键词:")
    for i, (word, freq) in enumerate(keywords[:10], 1):
        print(f"  {i:2d}. {word}: {freq}")
    
    # 过滤关键词
    filtered_keywords = keyword_filter.filter_keywords(
        keywords,
        min_freq=1,
        min_len=2,
        max_len=10
    )
    
    print(f"\n过滤后关键词数量: {len(filtered_keywords)}")
    print("过滤后前10个关键词:")
    for i, (word, freq) in enumerate(filtered_keywords[:10], 1):
        print(f"  {i:2d}. {word}: {freq}")
    
    # 显示过滤统计
    stats = keyword_filter.get_filter_stats(
        len(keywords),
        len(filtered_keywords)
    )
    
    print(f"\n过滤统计:")
    print(f"  原始数量: {stats['original_count']}")
    print(f"  过滤后数量: {stats['filtered_count']}")
    print(f"  移除数量: {stats['removed_count']}")
    print(f"  保留率: {stats['retention_rate']:.2%}")

def demo_wordcloud_styles():
    """演示词云风格"""
    print("\n" + "=" * 60)
    print("演示3: 词云风格 (已集成mask.jpg)")
    print("=" * 60)
    
    from wordcloud_generator import WordCloudGenerator
    
    # 测试关键词
    test_keywords = [
        ('Python', 15), ('编程', 12), ('开发', 10), ('数据', 8),
        ('人工智能', 7), ('机器学习', 6), ('Web', 6), ('框架', 5),
        ('库', 5), ('工具', 4), ('算法', 4), ('模型', 4),
        ('系统', 3), ('应用', 3), ('技术', 3), ('语言', 3)
    ]
    
    generator = WordCloudGenerator()
    
    # 获取默认风格
    styles = generator._get_default_styles()
    
    print("可用的词云风格 (均已集成mask.jpg):")
    for style_name, style_config in styles.items():
        mask_info = f" - mask: {style_config.get('mask_path', 'None')}"
        contour_info = f" - 轮廓: {style_config.get('contour_width', 0)}px"
        print(f"  - {style_name}{mask_info}{contour_info}")
    
    # 生成多种风格的词云
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    base_path = f"demo_output/demo_wordcloud_{timestamp}"
    
    results = generator.generate_multiple_styles(
        test_keywords,
        base_path,
        styles
    )
    
    print(f"\n生成的词云文件:")
    for style, success in results.items():
        status = "成功" if success else "失败"
        print(f"  {style}: {status}")
    
    # 检查mask.jpg文件
    if os.path.exists("mask.jpg"):
        print(f"\n[成功] mask.jpg文件存在，所有词云都将使用自定义形状")
    else:
        print(f"\n[警告] mask.jpg文件不存在，词云将使用默认矩形形状")

def demo_excel_export():
    """演示Excel导出功能"""
    print("\n" + "=" * 60)
    print("演示4: Excel导出功能")
    print("=" * 60)
    
    from excel_exporter import ExcelExporter
    
    # 测试数据
    test_keywords = [
        ('人工智能', 25), ('机器学习', 20), ('深度学习', 18),
        ('自然语言处理', 15), ('计算机视觉', 12), ('数据科学', 10),
        ('大数据', 8), ('云计算', 7), ('物联网', 6), ('区块链', 5),
        ('Python', 15), ('算法', 12), ('模型', 10), ('系统', 8),
        ('应用', 6), ('技术', 5), ('开发', 4), ('分析', 4)
    ]
    
    exporter = ExcelExporter()
    
    # 导出关键词
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filepath = f"demo_output/demo_excel_{timestamp}.xlsx"
    
    success = exporter.export_keywords_to_excel(
        test_keywords,
        filepath,
        include_chart=True,
        include_stats=True
    )
    
    if success:
        print(f"Excel导出成功: {filepath}")
        
        # 导出过滤详情
        # 模拟一些被过滤的关键词
        original_keywords = test_keywords + [('的', 30), ('是', 25), ('在', 20)]
        filtered_keywords = test_keywords
        
        filter_filepath = f"demo_output/demo_filter_details_{timestamp}.xlsx"
        filter_success = exporter.export_filter_details(
            original_keywords,
            filtered_keywords,
            filter_filepath
        )
        
        if filter_success:
            print(f"过滤详情导出成功: {filter_filepath}")
    else:
        print("Excel导出失败")

def demo_batch_processing():
    """演示批量处理"""
    print("\n" + "=" * 60)
    print("演示5: 批量处理")
    print("=" * 60)
    
    # 多个测试文本
    texts = [
        {
            'title': 'Python编程',
            'content': """
            Python是一种高级编程语言，具有简洁易读的语法。
            它支持多种编程范式，拥有丰富的标准库和第三方包。
            在Web开发、数据分析、人工智能等领域有广泛应用。
            """
        },
        {
            'title': '机器学习',
            'content': """
            机器学习是人工智能的一个分支，通过算法让计算机从数据中学习。
            包括监督学习、无监督学习和强化学习等多种方法。
            在图像识别、自然语言处理、推荐系统等领域有重要应用。
            """
        },
        {
            'title': '数据科学',
            'content': """
            数据科学是一个跨学科领域，结合统计学、计算机科学和领域知识。
            使用科学方法从数据中提取知识和见解。
            涉及数据收集、清洗、分析、可视化和建模等过程。
            """
        }
    ]
    
    system = WordCloudSystem({
        'output_dir': 'demo_output',
        'log_level': 'INFO'
    })
    
    results = []
    for i, text_data in enumerate(texts, 1):
        print(f"\n处理第{i}个文本: {text_data['title']}")
        
        result = system.process_text(text_data['content'], {
            'top_k': 20,
            'min_freq': 1,
            'styles': ['default'],
            'include_chart': True
        })
        
        if result['success']:
            print(f"  处理成功，生成 {result['filtered_keywords_count']} 个关键词")
            results.append(result)
        else:
            print(f"  处理失败: {result['error']}")
    
    print(f"\n批量处理完成，共处理 {len(results)} 个文本")
    
    # 创建对比报告
    if len(results) > 1:
        datasets = {}
        for i, result in enumerate(results):
            datasets[f"文本_{i+1}"] = result['top_keywords']
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        comparison_path = f"demo_output/demo_comparison_{timestamp}.xlsx"
        
        success = system.excel_exporter.export_multiple_datasets(
            datasets,
            comparison_path,
            include_comparison=True
        )
        
        if success:
            print(f"对比报告已生成: {comparison_path}")

def main():
    """主演示函数"""
    print("词云生成系统功能演示")
    print("=" * 60)
    
    # 创建输出目录
    os.makedirs('demo_output', exist_ok=True)
    
    try:
        # 运行各种演示
        demo_text_processing()
        demo_keyword_filtering()
        demo_wordcloud_styles()
        demo_excel_export()
        demo_batch_processing()
        
        print("\n" + "=" * 60)
        print("所有演示完成!")
        print("=" * 60)
        print(f"生成的文件位于: demo_output 目录")
        
        # 显示生成的文件
        if os.path.exists('demo_output'):
            files = os.listdir('demo_output')
            print(f"共生成 {len(files)} 个文件:")
            for file in files:
                filepath = os.path.join('demo_output', file)
                filesize = os.path.getsize(filepath)
                print(f"  - {file} ({filesize} bytes)")
        
    except Exception as e:
        print(f"演示过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()