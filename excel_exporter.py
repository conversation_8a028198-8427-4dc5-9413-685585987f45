"""
Excel数据导出模块
负责将关键词数据导出到Excel文件
"""
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
from openpyxl.chart import Bar<PERSON>hart, Reference
from typing import List, Tuple, Dict, Optional
import logging
import os

class ExcelExporter:
    """Excel导出器类"""
    
    def __init__(self):
        self.styles = self._setup_styles()
    
    def _setup_styles(self) -> Dict[str, Dict]:
        """
        设置Excel样式
        
        Returns:
            样式字典
        """
        return {
            'header_font': Font(name='Arial', size=12, bold=True, color='FFFFFF'),
            'header_fill': Pattern<PERSON>ill(start_color='366092', end_color='366092', fill_type='solid'),
            'header_alignment': Alignment(horizontal='center', vertical='center', wrap_text=True),
            'data_font': Font(name='Arial', size=11),
            'data_alignment': Alignment(horizontal='left', vertical='center'),
            'number_alignment': Alignment(horizontal='center', vertical='center'),
            'border': Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
        }
    
    def export_keywords_to_excel(self, 
                                 keywords: List[Tuple[str, int]], 
                                 filepath: str,
                                 sheet_name: str = '关键词统计',
                                 include_chart: bool = True,
                                 include_stats: bool = True) -> bool:
        """
        导出关键词到Excel
        
        Args:
            keywords: 关键词和词频列表
            filepath: 保存路径
            sheet_name: 工作表名称
            include_chart: 是否包含图表
            include_stats: 是否包含统计信息
            
        Returns:
            是否导出成功
        """
        try:
            # 创建DataFrame
            df = pd.DataFrame(keywords, columns=['关键词', '词频'])
            
            # 添加排名列
            df['排名'] = range(1, len(df) + 1)
            
            # 重新排列列
            df = df[['排名', '关键词', '词频']]
            
            # 创建Excel工作簿
            wb = Workbook()
            ws = wb.active
            ws.title = sheet_name
            
            # 写入数据
            self._write_data_to_sheet(ws, df)
            
            # 添加统计信息
            if include_stats:
                self._add_statistics_sheet(wb, df)
            
            # 添加图表
            if include_chart:
                self._add_chart_to_sheet(ws, df)
            
            # 确保目录存在
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            
            # 保存文件
            wb.save(filepath)
            
            logging.info(f"Excel文件已保存到: {filepath}")
            return True
            
        except Exception as e:
            logging.error(f"导出Excel失败: {str(e)}")
            return False
    
    def _write_data_to_sheet(self, ws, df: pd.DataFrame):
        """
        写入数据到工作表
        
        Args:
            ws: 工作表对象
            df: DataFrame对象
        """
        # 写入表头
        headers = df.columns.tolist()
        for col_num, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col_num, value=header)
            cell.font = self.styles['header_font']
            cell.fill = self.styles['header_fill']
            cell.alignment = self.styles['header_alignment']
            cell.border = self.styles['border']
        
        # 写入数据
        for row_num, row in enumerate(df.itertuples(index=False), 2):
            for col_num, value in enumerate(row, 1):
                cell = ws.cell(row=row_num, column=col_num, value=value)
                cell.font = self.styles['data_font']
                
                # 设置对齐方式
                if col_num == 1:  # 排名列
                    cell.alignment = self.styles['number_alignment']
                else:
                    cell.alignment = self.styles['data_alignment']
                
                cell.border = self.styles['border']
        
        # 调整列宽
        self._adjust_column_widths(ws, df)
    
    def _adjust_column_widths(self, ws, df: pd.DataFrame):
        """
        调整列宽
        
        Args:
            ws: 工作表对象
            df: DataFrame对象
        """
        # 排名列
        ws.column_dimensions['A'].width = 8
        
        # 关键词列
        max_keyword_length = max(df['关键词'].astype(str).str.len())
        ws.column_dimensions['B'].width = min(max_keyword_length + 2, 30)
        
        # 词频列
        ws.column_dimensions['C'].width = 10
    
    def _add_statistics_sheet(self, wb, df: pd.DataFrame):
        """
        添加统计信息工作表
        
        Args:
            wb: 工作簿对象
            df: DataFrame对象
        """
        ws = wb.create_sheet('统计信息')
        
        # 计算统计信息
        stats = {
            '总关键词数': len(df),
            '最高词频': df['词频'].max(),
            '最低词频': df['词频'].min(),
            '平均词频': round(df['词频'].mean(), 2),
            '词频中位数': df['词频'].median(),
            '词频标准差': round(df['词频'].std(), 2),
            '词频总和': df['词频'].sum()
        }
        
        # 写入统计信息
        row = 1
        for key, value in stats.items():
            ws.cell(row=row, column=1, value=key)
            ws.cell(row=row, column=2, value=value)
            row += 1
        
        # 设置样式
        for row in range(1, len(stats) + 1):
            for col in range(1, 3):
                cell = ws.cell(row=row, column=col)
                cell.font = self.styles['data_font']
                cell.alignment = self.styles['data_alignment']
                cell.border = self.styles['border']
        
        # 调整列宽
        ws.column_dimensions['A'].width = 15
        ws.column_dimensions['B'].width = 15
    
    def _add_chart_to_sheet(self, ws, df: pd.DataFrame):
        """
        添加图表到工作表
        
        Args:
            ws: 工作表对象
            df: DataFrame对象
        """
        try:
            # 创建柱状图
            chart = BarChart()
            chart.type = "col"
            chart.style = 10
            chart.title = "关键词词频统计"
            chart.y_axis.title = '词频'
            chart.x_axis.title = '关键词'
            
            # 设置数据范围
            data = Reference(ws, min_col=3, min_row=1, max_row=len(df)+1, max_col=3)
            cats = Reference(ws, min_col=2, min_row=2, max_row=len(df)+1, max_col=2)
            
            chart.add_data(data, titles_from_data=True)
            chart.set_categories(cats)
            
            # 设置图表大小
            chart.width = 15
            chart.height = 10
            
            # 添加图表到工作表
            ws.add_chart(chart, "E2")
            
        except Exception as e:
            logging.warning(f"添加图表失败: {str(e)}")
    
    def export_multiple_datasets(self, 
                                 datasets: Dict[str, List[Tuple[str, int]]], 
                                 filepath: str,
                                 include_comparison: bool = True) -> bool:
        """
        导出多个数据集到Excel
        
        Args:
            datasets: 数据集字典 {名称: 关键词列表}
            filepath: 保存路径
            include_comparison: 是否包含对比表
            
        Returns:
            是否导出成功
        """
        try:
            # 创建Excel工作簿
            wb = Workbook()
            
            # 删除默认工作表
            wb.remove(wb.active)
            
            # 为每个数据集创建工作表
            for name, keywords in datasets.items():
                df = pd.DataFrame(keywords, columns=['关键词', '词频'])
                df['排名'] = range(1, len(df) + 1)
                df = df[['排名', '关键词', '词频']]
                
                ws = wb.create_sheet(name)
                self._write_data_to_sheet(ws, df)
            
            # 添加对比表
            if include_comparison and len(datasets) > 1:
                self._add_comparison_sheet(wb, datasets)
            
            # 保存文件
            wb.save(filepath)
            
            logging.info(f"多数据集Excel文件已保存到: {filepath}")
            return True
            
        except Exception as e:
            logging.error(f"导出多数据集Excel失败: {str(e)}")
            return False
    
    def _add_comparison_sheet(self, wb, datasets: Dict[str, List[Tuple[str, int]]]):
        """
        添加对比工作表
        
        Args:
            wb: 工作簿对象
            datasets: 数据集字典
        """
        ws = wb.create_sheet('对比分析')
        
        # 获取所有关键词
        all_keywords = set()
        for keywords in datasets.values():
            all_keywords.update([word for word, _ in keywords])
        
        # 创建对比数据
        comparison_data = []
        for keyword in sorted(all_keywords):
            row = {'关键词': keyword}
            for name, keywords in datasets.items():
                freq_dict = dict(keywords)
                row[name] = freq_dict.get(keyword, 0)
            comparison_data.append(row)
        
        # 转换为DataFrame
        df = pd.DataFrame(comparison_data)
        
        # 写入数据
        self._write_data_to_sheet(ws, df)
    
    def export_filter_details(self, 
                             original_keywords: List[Tuple[str, int]],
                             filtered_keywords: List[Tuple[str, int]],
                             filepath: str) -> bool:
        """
        导出过滤详情
        
        Args:
            original_keywords: 原始关键词
            filtered_keywords: 过滤后关键词
            filepath: 保存路径
            
        Returns:
            是否导出成功
        """
        try:
            wb = Workbook()
            
            # 原始数据工作表
            ws1 = wb.active
            ws1.title = '原始关键词'
            df1 = pd.DataFrame(original_keywords, columns=['关键词', '词频'])
            df1['排名'] = range(1, len(df1) + 1)
            df1 = df1[['排名', '关键词', '词频']]
            self._write_data_to_sheet(ws1, df1)
            
            # 过滤后数据工作表
            ws2 = wb.create_sheet('过滤后关键词')
            df2 = pd.DataFrame(filtered_keywords, columns=['关键词', '词频'])
            df2['排名'] = range(1, len(df2) + 1)
            df2 = df2[['排名', '关键词', '词频']]
            self._write_data_to_sheet(ws2, df2)
            
            # 过滤详情工作表
            ws3 = wb.create_sheet('过滤详情')
            
            original_set = set([word for word, _ in original_keywords])
            filtered_set = set([word for word, _ in filtered_keywords])
            
            removed_keywords = original_set - filtered_set
            
            details_data = []
            for keyword in removed_keywords:
                freq_dict = dict(original_keywords)
                details_data.append({'关键词': keyword, '词频': freq_dict[keyword], '状态': '已过滤'})
            
            for keyword in filtered_set:
                freq_dict = dict(filtered_keywords)
                details_data.append({'关键词': keyword, '词频': freq_dict[keyword], '状态': '保留'})
            
            df3 = pd.DataFrame(details_data)
            self._write_data_to_sheet(ws3, df3)
            
            # 保存文件
            wb.save(filepath)
            
            logging.info(f"过滤详情Excel文件已保存到: {filepath}")
            return True
            
        except Exception as e:
            logging.error(f"导出过滤详情失败: {str(e)}")
            return False