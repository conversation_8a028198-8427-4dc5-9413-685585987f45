"""
中文分词和词频统计模块
负责对文本进行分词、词频统计和关键词提取
"""
import jieba
import jieba.analyse
from collections import Counter
import re
from typing import List, Dict, Tuple, Set
import logging

class TextProcessor:
    """文本处理器类"""
    
    def __init__(self):
        # 初始化jieba分词
        jieba.initialize()
        
        # 加载自定义词典（如果需要）
        # jieba.load_userdict("user_dict.txt")
        
        # 设置停用词
        self.stopwords = self._load_stopwords()
        
        # 设置关键词过滤词
        self.filter_words = self._load_filter_words()
        
    def _load_stopwords(self) -> Set[str]:
        """
        加载停用词表
        
        Returns:
            停用词集合
        """
        # 常见中文停用词
        stopwords = {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
            '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
            '自己', '这', '那', '现在', '可以', '但是', '还是', '因为', '什么', '如果',
            '所以', '这样', '我们', '他们', '她们', '它们', '这里', '那里', '为什么',
            '怎么', '怎样', '多少', '几个', '什么时候', '哪里', '谁', '怎么样', '如何',
            '或者', '而且', '然后', '同时', '另外', '此外', '除了', '关于', '对于',
            '通过', '根据', '按照', '由于', '虽然', '尽管', '不管', '无论', '只要',
            '只有', '不但', '不仅', '既然', '如果', '要是', '假如', '倘若', '要是',
            '啊', '呀', '哟', '哦', '唉', '嗯', '哈', '啦', '吧', '呢', '嘛', '呗',
            '了', '着', '过', '来', '去', '上', '下', '里', '外', '中', '间', '内',
            '第', '们', '之', '乎', '者', '也', '矣', '焉', '哉', '而', '已', '夫',
            '盖', '云', '曰', '今', '故', '此', '斯', '彼', '其', '何', '孰', '安',
            '所', '者', '诸', '凡', '各', '每', '各', '某', '另', '该', '本', '此',
            '我', '你', '他', '她', '它', '们', '自己', '人家', '大家', '谁', '什么',
            '这', '那', '这些', '那些', '这样', '那样', '这里', '那里', '这么', '那么',
            '多少', '几', '一些', '许多', '所有', '全部', '整个', '每个', '各个',
            '一个', '两个', '三个', '第一', '第二', '第三', '最后', '其次', '另外',
            '而且', '并且', '或者', '还是', '但是', '可是', '然而', '不过', '只是',
            '因为', '所以', '由于', '因此', '于是', '结果', '从而', '以便', '为了',
            '如果', '要是', '假如', '倘若', '要是', '否则', '不然', '万一', '即使',
            '虽然', '尽管', '不管', '无论', '只要', '只有', '不但', '不仅', '既然',
            '当', '在', '从', '到', '向', '对', '给', '为', '比', '跟', '同', '与',
            '和', '或', '而', '因为', '由于', '为了', '根据', '按照', '通过', '经过',
            '关于', '对于', '至于', '除了', '除非', '如果', '虽然', '即使', '尽管',
            '无论', '不管', '只要', '只有', '不但', '不仅', '既然', '要是', '假如',
            '或许', '可能', '大概', '也许', '约莫', '左右', '上下', '前后', '远近',
            '非常', '特别', '十分', '相当', '比较', '稍微', '有点', '有些', '多少',
            '很', '太', '最', '更', '越', '较', '稍', '略', '颇', '甚', '极', '顶',
            '又', '再', '还', '也', '都', '总', '共', '全', '皆', '俱', '各', '每',
            '一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '百', '千',
            '万', '亿', '零', '半', '几', '两', '双', '对', '副', '群', '批', '套',
            '个', '只', '条', '张', '件', '本', '部', '台', '架', '辆', '艘', '架',
            '名', '位', '口', '家', '户', '村', '镇', '城', '省', '国', '州', '县',
            '年', '月', '日', '时', '分', '秒', '周', '季', '度', '世纪', '年代',
            '元', '角', '分', '厘', '毫', '微', '公斤', '克', '吨', '米', '厘米',
            '毫米', '公里', '平方米', '立方米', '升', '毫升', '小时', '分钟', '秒钟'
        }
        
        return stopwords
    
    def _load_filter_words(self) -> Set[str]:
        """
        加载需要过滤的关键词
        
        Returns:
            过滤词集合
        """
        # 常见的需要过滤的词汇
        filter_words = {
            '可以', '能够', '应该', '需要', '必须', '要', '想', '希望', '觉得',
            '认为', '感觉', '发现', '知道', '了解', '明白', '理解', '记得', '忘记',
            '记得', '忘记', '看见', '听到', '听说', '想到', '遇到', '碰到', '遇到',
            '发生', '出现', '存在', '属于', '成为', '变成', '当作', '看作', '认为',
            '开始', '结束', '继续', '停止', '完成', '实现', '达到', '获得', '取得',
            '进行', '开展', '实施', '执行', '操作', '处理', '解决', '面对', '接受',
            '拒绝', '同意', '反对', '支持', '反对', '喜欢', '讨厌', '爱', '恨',
            '高兴', '快乐', '开心', '难过', '伤心', '痛苦', '兴奋', '激动', '平静',
            '重要', '必要', '可能', '大概', '也许', '或许', '一定', '肯定', '当然',
            '确实', '实在', '真正', '确实', '真的', '假的', '错误的', '正确的'
        }
        
        return filter_words
    
    def extract_keywords(self, text: str, top_k: int = 100) -> List[Tuple[str, int]]:
        """
        提取关键词和词频
        
        Args:
            text: 输入文本
            top_k: 返回前K个关键词
            
        Returns:
            关键词和词频的列表
        """
        try:
            # 使用jieba进行分词
            words = jieba.lcut(text)
            
            # 过滤词性，只保留名词、动词、形容词等有意义的词
            meaningful_words = []
            for word, flag in jieba.posseg.cut(text):
                # 过滤条件：词性合适、长度合适、不在停用词中、不在过滤词中
                if (flag.startswith(('n', 'v', 'a', 'i', 'j')) and  # 名词、动词、形容词、成语、简称
                    len(word) >= 2 and  # 至少2个字符
                    word not in self.stopwords and
                    word not in self.filter_words and
                    not word.isdigit() and  # 不是纯数字
                    not re.match(r'^[a-zA-Z]+$', word)):  # 不是纯英文
                    
                    meaningful_words.append(word)
            
            # 统计词频
            word_freq = Counter(meaningful_words)
            
            # 返回前K个高频词
            return word_freq.most_common(top_k)
            
        except Exception as e:
            logging.error(f"关键词提取失败: {str(e)}")
            return []
    
    def extract_tfidf_keywords(self, text: str, top_k: int = 50) -> List[Tuple[str, float]]:
        """
        使用TF-IDF提取关键词
        
        Args:
            text: 输入文本
            top_k: 返回前K个关键词
            
        Returns:
            关键词和TF-IDF权重的列表
        """
        try:
            # 使用jieba的TF-IDF算法
            keywords = jieba.analyse.extract_tags(
                text, 
                topK=top_k, 
                withWeight=True,
                allowPOS=('n', 'v', 'a', 'i', 'j')
            )
            
            return keywords
            
        except Exception as e:
            logging.error(f"TF-IDF关键词提取失败: {str(e)}")
            return []
    
    def filter_keywords(self, keywords: List[Tuple[str, int]], 
                       min_freq: int = 2, 
                       max_length: int = 10) -> List[Tuple[str, int]]:
        """
        过滤关键词
        
        Args:
            keywords: 关键词列表
            min_freq: 最小词频
            max_length: 最大词长
            
        Returns:
            过滤后的关键词列表
        """
        filtered = []
        
        for word, freq in keywords:
            if (freq >= min_freq and 
                len(word) <= max_length and
                not word.isdigit() and
                not re.match(r'^[a-zA-Z]+$', word) and
                word not in self.filter_words):
                filtered.append((word, freq))
        
        return filtered
    
    def get_word_frequency_stats(self, keywords: List[Tuple[str, int]]) -> Dict[str, int]:
        """
        获取词频统计信息
        
        Args:
            keywords: 关键词列表
            
        Returns:
            词频统计信息
        """
        if not keywords:
            return {}
        
        frequencies = [freq for _, freq in keywords]
        
        return {
            'total_words': len(keywords),
            'max_freq': max(frequencies),
            'min_freq': min(frequencies),
            'avg_freq': sum(frequencies) / len(frequencies),
            'median_freq': sorted(frequencies)[len(frequencies) // 2]
        }