# 词云生成系统使用示例

## 安装依赖

```bash
pip install -r requirements.txt
```

## 基本使用

### 1. 处理单个URL

```bash
python main.py --url "https://example.com/article" --styles default blue_theme green_theme --include-chart --include-stats
```

### 2. 处理多个URL

```bash
python main.py --urls "https://example.com/article1" "https://example.com/article2" --output-dir results
```

### 3. 处理文本内容

```bash
python main.py --text "这是一段测试文本，用于生成词云。" --min-freq 1 --top-k 20
```

### 4. 处理文本文件

```bash
python main.py --text-file input.txt --export-filter-details
```

## 参数说明

- `--url`: 单个文章URL
- `--urls`: 多个文章URL
- `--text`: 直接输入文本内容
- `--text-file`: 文本文件路径
- `--output-dir`: 输出目录（默认：output）
- `--top-k`: 提取关键词数量（默认：100）
- `--min-freq`: 最小词频（默认：2）
- `--min-len`: 最小词长（默认：2）
- `--max-len`: 最大词长（默认：10）
- `--styles`: 词云风格（默认：default blue_theme green_theme）
- `--include-chart`: 包含图表
- `--include-stats`: 包含统计信息
- `--export-filter-details`: 导出过滤详情
- `--log-level`: 日志级别（默认：INFO）

## 词云风格

系统支持多种词云风格：
- `default`: 默认风格
- `blue_theme`: 蓝色主题
- `green_theme`: 绿色主题
- `red_theme`: 红色主题
- `rainbow`: 彩虹主题
- `dark`: 暗色主题

## 编程接口使用

```python
from main import WordCloudSystem

# 创建系统实例
system = WordCloudSystem({
    'output_dir': 'custom_output',
    'log_level': 'INFO'
})

# 处理URL
result = system.process_url(
    "https://example.com/article",
    options={
        'top_k': 50,
        'min_freq': 2,
        'styles': ['blue_theme', 'green_theme'],
        'include_chart': True
    }
)

print(f"处理结果: {result}")
```

## 输出文件

系统会生成以下文件：
1. **词云图片**: PNG格式的词云图
2. **Excel文件**: 包含关键词统计、图表和统计信息
3. **过滤详情**: 如果启用，会显示过滤前后的对比
4. **日志文件**: wordcloud_system.log

## 测试系统

运行测试脚本验证系统功能：

```bash
python test_system.py
```

## 注意事项

1. 确保网络连接正常，用于下载文章内容
2. 某些网站可能有反爬虫机制，可能导致下载失败
3. 中文字体需要系统支持，建议使用Windows系统
4. 输出目录会自动创建，无需手动创建
5. 大文件处理可能需要较长时间，请耐心等待

## 故障排除

### 1. 依赖安装失败
```bash
# 使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 2. 字体问题
如果词云中的中文显示为方框，请确保系统安装了中文字体。

### 3. 网络问题
如果URL处理失败，可以尝试：
- 检查网络连接
- 更换其他URL
- 使用文本文件代替URL

### 4. 内存不足
处理大文本时，可以调整参数：
```bash
python main.py --text-file large_text.txt --top-k 50 --min-freq 3
```