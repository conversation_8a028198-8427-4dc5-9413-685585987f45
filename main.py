"""
词云生成系统主程序
整合所有模块，提供完整的词云生成流程
"""
import argparse
import logging
import os
import sys
from typing import List, Dict, Optional
from datetime import datetime

# 导入自定义模块
from article_downloader import ArticleDownloader
from text_processor import TextProcessor
from keyword_filter import KeywordFilter
from wordcloud_generator import WordCloudGenerator
from excel_exporter import ExcelExporter

class WordCloudSystem:
    """词云生成系统主类"""
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化词云系统
        
        Args:
            config: 配置字典
        """
        self.config = config or {}
        
        # 设置日志
        self._setup_logging()
        
        # 初始化各模块
        self.downloader = ArticleDownloader()
        self.processor = TextProcessor()
        self.filter = KeywordFilter()
        self.wordcloud_gen = WordCloudGenerator()
        self.excel_exporter = ExcelExporter()
        
        # 设置输出目录
        self.output_dir = self.config.get('output_dir', 'output')
        os.makedirs(self.output_dir, exist_ok=True)
        
        logging.info("词云生成系统初始化完成")
    
    def _setup_logging(self):
        """设置日志"""
        log_level = self.config.get('log_level', 'INFO')
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        
        logging.basicConfig(
            level=getattr(logging, log_level),
            format=log_format,
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler('wordcloud_system.log', encoding='utf-8')
            ]
        )
    
    def process_url(self, url: str, options: Optional[Dict] = None) -> Dict[str, any]:
        """
        处理单个URL
        
        Args:
            url: 文章URL
            options: 处理选项
            
        Returns:
            处理结果
        """
        options = options or {}
        
        try:
            logging.info(f"开始处理URL: {url}")
            
            # 1. 下载文章
            logging.info("正在下载文章...")
            article_content = self.downloader.download_article(url)
            
            if not article_content:
                logging.error("文章下载失败")
                return {'success': False, 'error': '文章下载失败'}
            
            logging.info(f"文章下载成功，内容长度: {len(article_content)}")
            
            # 2. 提取关键词
            logging.info("正在提取关键词...")
            top_k = options.get('top_k', 100)
            keywords = self.processor.extract_keywords(article_content, top_k)
            
            if not keywords:
                logging.error("关键词提取失败")
                return {'success': False, 'error': '关键词提取失败'}
            
            logging.info(f"提取到 {len(keywords)} 个关键词")
            
            # 3. 过滤关键词
            logging.info("正在过滤关键词...")
            filtered_keywords = self.filter.filter_keywords(
                keywords,
                min_freq=options.get('min_freq', 2),
                min_len=options.get('min_len', 2),
                max_len=options.get('max_len', 10)
            )
            
            logging.info(f"过滤后保留 {len(filtered_keywords)} 个关键词")
            
            # 4. 生成词云
            logging.info("正在生成词云...")
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 生成多种风格的词云
            wordcloud_files = {}
            styles = options.get('styles', ['default', 'blue_theme', 'green_theme'])
            
            for style in styles:
                filename = f"wordcloud_{timestamp}_{style}.png"
                filepath = os.path.join(self.output_dir, filename)
                
                style_config = self.wordcloud_gen._get_default_styles().get(style, {})
                
                success = self.wordcloud_gen.generate_and_save(
                    filtered_keywords,
                    filepath,
                    **style_config
                )
                
                if success:
                    wordcloud_files[style] = filepath
                    logging.info(f"词云已保存: {filepath}")
            
            # 5. 导出Excel
            logging.info("正在导出Excel文件...")
            excel_filename = f"keywords_{timestamp}.xlsx"
            excel_filepath = os.path.join(self.output_dir, excel_filename)
            
            excel_success = self.excel_exporter.export_keywords_to_excel(
                filtered_keywords,
                excel_filepath,
                include_chart=options.get('include_chart', True),
                include_stats=options.get('include_stats', True)
            )
            
            if excel_success:
                logging.info(f"Excel文件已保存: {excel_filepath}")
            else:
                logging.error("Excel导出失败")
            
            # 6. 导出过滤详情
            if options.get('export_filter_details', False):
                filter_filename = f"filter_details_{timestamp}.xlsx"
                filter_filepath = os.path.join(self.output_dir, filter_filename)
                
                filter_success = self.excel_exporter.export_filter_details(
                    keywords,
                    filtered_keywords,
                    filter_filepath
                )
                
                if filter_success:
                    logging.info(f"过滤详情已保存: {filter_filepath}")
            
            return {
                'success': True,
                'url': url,
                'article_length': len(article_content),
                'original_keywords_count': len(keywords),
                'filtered_keywords_count': len(filtered_keywords),
                'wordcloud_files': wordcloud_files,
                'excel_file': excel_filepath if excel_success else None,
                'top_keywords': filtered_keywords[:10]  # 前10个关键词
            }
            
        except Exception as e:
            logging.error(f"处理URL失败: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def process_multiple_urls(self, urls: List[str], options: Optional[Dict] = None) -> Dict[str, any]:
        """
        处理多个URL
        
        Args:
            urls: URL列表
            options: 处理选项
            
        Returns:
            处理结果
        """
        options = options or {}
        results = []
        
        for i, url in enumerate(urls, 1):
            logging.info(f"处理第 {i}/{len(urls)} 个URL")
            
            result = self.process_url(url, options)
            results.append(result)
            
            # 如果失败，继续处理下一个
            if not result['success']:
                logging.warning(f"URL处理失败: {url}")
                continue
        
        # 生成汇总报告
        successful_results = [r for r in results if r['success']]
        
        if successful_results:
            # 创建对比Excel
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            comparison_filename = f"comparison_{timestamp}.xlsx"
            comparison_filepath = os.path.join(self.output_dir, comparison_filename)
            
            datasets = {}
            for i, result in enumerate(successful_results):
                datasets[f"URL_{i+1}"] = result['top_keywords']
            
            self.excel_exporter.export_multiple_datasets(
                datasets,
                comparison_filepath,
                include_comparison=True
            )
            
            logging.info(f"对比报告已保存: {comparison_filepath}")
        
        return {
            'total_urls': len(urls),
            'successful_urls': len(successful_results),
            'failed_urls': len(results) - len(successful_results),
            'results': results,
            'comparison_file': comparison_filepath if successful_results else None
        }
    
    def process_text(self, text: str, options: Optional[Dict] = None) -> Dict[str, any]:
        """
        处理文本内容
        
        Args:
            text: 文本内容
            options: 处理选项
            
        Returns:
            处理结果
        """
        options = options or {}
        
        try:
            logging.info("开始处理文本内容")
            
            # 1. 提取关键词
            logging.info("正在提取关键词...")
            top_k = options.get('top_k', 100)
            keywords = self.processor.extract_keywords(text, top_k)
            
            if not keywords:
                logging.error("关键词提取失败")
                return {'success': False, 'error': '关键词提取失败'}
            
            logging.info(f"提取到 {len(keywords)} 个关键词")
            
            # 2. 过滤关键词
            logging.info("正在过滤关键词...")
            filtered_keywords = self.filter.filter_keywords(
                keywords,
                min_freq=options.get('min_freq', 2),
                min_len=options.get('min_len', 2),
                max_len=options.get('max_len', 10)
            )
            
            logging.info(f"过滤后保留 {len(filtered_keywords)} 个关键词")
            
            # 3. 生成词云和Excel
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 生成词云
            wordcloud_filename = f"wordcloud_text_{timestamp}.png"
            wordcloud_filepath = os.path.join(self.output_dir, wordcloud_filename)
            
            wordcloud_success = self.wordcloud_gen.generate_and_save(
                filtered_keywords,
                wordcloud_filepath
            )
            
            # 导出Excel
            excel_filename = f"keywords_text_{timestamp}.xlsx"
            excel_filepath = os.path.join(self.output_dir, excel_filename)
            
            excel_success = self.excel_exporter.export_keywords_to_excel(
                filtered_keywords,
                excel_filepath
            )
            
            return {
                'success': True,
                'text_length': len(text),
                'original_keywords_count': len(keywords),
                'filtered_keywords_count': len(filtered_keywords),
                'wordcloud_file': wordcloud_filepath if wordcloud_success else None,
                'excel_file': excel_filepath if excel_success else None,
                'top_keywords': filtered_keywords[:10]
            }
            
        except Exception as e:
            logging.error(f"处理文本失败: {str(e)}")
            return {'success': False, 'error': str(e)}

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='词云生成系统')
    parser.add_argument('--url', type=str, help='文章URL')
    parser.add_argument('--urls', type=str, nargs='+', help='多个文章URL')
    parser.add_argument('--text', type=str, help='文本内容')
    parser.add_argument('--text-file', type=str, help='文本文件路径')
    parser.add_argument('--output-dir', type=str, default='output', help='输出目录')
    parser.add_argument('--top-k', type=int, default=100, help='关键词数量')
    parser.add_argument('--min-freq', type=int, default=2, help='最小词频')
    parser.add_argument('--min-len', type=int, default=2, help='最小词长')
    parser.add_argument('--max-len', type=int, default=10, help='最大词长')
    parser.add_argument('--styles', type=str, nargs='+', 
                       default=['default', 'blue_theme', 'green_theme'],
                       help='词云风格')
    parser.add_argument('--include-chart', action='store_true', help='包含图表')
    parser.add_argument('--include-stats', action='store_true', help='包含统计信息')
    parser.add_argument('--export-filter-details', action='store_true', help='导出过滤详情')
    parser.add_argument('--log-level', type=str, default='INFO', help='日志级别')
    
    args = parser.parse_args()
    
    # 创建配置
    config = {
        'output_dir': args.output_dir,
        'log_level': args.log_level
    }
    
    # 创建系统实例
    system = WordCloudSystem(config)
    
    # 处理选项
    options = {
        'top_k': args.top_k,
        'min_freq': args.min_freq,
        'min_len': args.min_len,
        'max_len': args.max_len,
        'styles': args.styles,
        'include_chart': args.include_chart,
        'include_stats': args.include_stats,
        'export_filter_details': args.export_filter_details
    }
    
    # 根据参数选择处理方式
    if args.url:
        # 处理单个URL
        result = system.process_url(args.url, options)
        print(f"处理结果: {result}")
    
    elif args.urls:
        # 处理多个URL
        result = system.process_multiple_urls(args.urls, options)
        print(f"处理结果: {result}")
    
    elif args.text:
        # 处理文本
        result = system.process_text(args.text, options)
        print(f"处理结果: {result}")
    
    elif args.text_file:
        # 处理文本文件
        try:
            with open(args.text_file, 'r', encoding='utf-8') as f:
                text = f.read()
            result = system.process_text(text, options)
            print(f"处理结果: {result}")
        except Exception as e:
            print(f"读取文件失败: {str(e)}")
    
    else:
        print("请指定处理内容：--url, --urls, --text, 或 --text-file")
        parser.print_help()

if __name__ == "__main__":
    main()