"""
文章下载和解析模块
负责从指定URL下载文章内容并提取文本
"""
import requests
from bs4 import BeautifulSoup
import re
from typing import Optional, List, Dict
import logging

class ArticleDownloader:
    """文章下载器类"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
    def download_article(self, url: str) -> Optional[str]:
        """
        下载文章内容
        
        Args:
            url: 文章URL
            
        Returns:
            文章文本内容，下载失败返回None
        """
        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            response.encoding = response.apparent_encoding
            
            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 移除脚本和样式
            for script in soup(["script", "style"]):
                script.decompose()
            
            # 提取文本
            text = self._extract_text(soup)
            
            if text and len(text.strip()) > 100:  # 确保有足够的内容
                return text
            else:
                logging.warning(f"文章内容过少或为空: {url}")
                return None
                
        except Exception as e:
            logging.error(f"下载文章失败 {url}: {str(e)}")
            return None
    
    def _extract_text(self, soup: BeautifulSoup) -> str:
        """
        从BeautifulSoup对象中提取文本
        
        Args:
            soup: BeautifulSoup对象
            
        Returns:
            提取的文本内容
        """
        # 常见的文章内容选择器
        content_selectors = [
            'article', '.article-content', '.post-content', '.entry-content',
            '.content', 'main', '.main-content', '#content'
        ]
        
        text = ""
        
        # 尝试使用选择器提取内容
        for selector in content_selectors:
            elements = soup.select(selector)
            if elements:
                text = ' '.join([elem.get_text(strip=True) for elem in elements])
                if len(text) > 200:  # 确保内容足够长
                    break
        
        # 如果选择器没有找到足够内容，提取整个页面的文本
        if not text or len(text) < 200:
            text = soup.get_text()
        
        # 清理文本
        text = self._clean_text(text)
        
        return text
    
    def _clean_text(self, text: str) -> str:
        """
        清理文本内容
        
        Args:
            text: 原始文本
            
        Returns:
            清理后的文本
        """
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 移除特殊字符和符号
        text = re.sub(r'[^\w\s\u4e00-\u9fff，。！？、；：""''（）【】《》\.\!\?\,\;\:\(\)\[\]\{\}\<\>]', '', text)
        
        # 移除过短的行
        lines = [line.strip() for line in text.split('\n') if len(line.strip()) > 10]
        
        return ' '.join(lines)
    
    def download_multiple_articles(self, urls: List[str]) -> Dict[str, Optional[str]]:
        """
        批量下载多篇文章
        
        Args:
            urls: URL列表
            
        Returns:
            字典：{url: 文章内容}
        """
        results = {}
        
        for url in urls:
            logging.info(f"正在下载: {url}")
            content = self.download_article(url)
            results[url] = content
            
        return results