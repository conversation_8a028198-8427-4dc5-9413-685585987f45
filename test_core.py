"""
简化版词云生成系统测试
不依赖网络请求，仅测试核心功能
"""
import os
import sys
import logging
from datetime import datetime

# 模拟导入（如果依赖不存在）
try:
    from text_processor import TextProcessor
    from keyword_filter import KeywordFilter
    from wordcloud_generator import WordCloudGenerator
    from excel_exporter import ExcelExporter
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请先安装依赖: pip install -r requirements.txt")
    sys.exit(1)

def test_core_functionality():
    """测试核心功能"""
    print("开始测试核心功能...")
    
    # 创建输出目录
    output_dir = 'test_output'
    os.makedirs(output_dir, exist_ok=True)
    
    # 测试文本
    test_text = """
    人工智能是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。
    该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等。
    人工智能从诞生以来，理论和技术日益成熟，应用领域也不断扩大。
    可以设想，未来人工智能带来的科技产品，将会是人类智慧的容器。
    人工智能可以对人的意识、思维的信息过程的模拟。
    人工智能不是人的智能，但能像人那样思考、也可能超过人的智能。
    人工智能是一门极富挑战性的科学，从事这项工作的人必须懂得计算机知识，心理学和哲学。
    人工智能是包括十分广泛的科学，它由不同的领域组成，如机器学习，计算机视觉等等。
    总的说来，人工智能研究的一个主要目标是使机器能够胜任一些通常需要人类智能才能完成的复杂工作。
    机器学习是人工智能的一个分支，它使用算法和统计模型来让计算机系统在没有明确编程的情况下学习和改进。
    深度学习是机器学习的一个子领域，它基于人工神经网络，特别是多层神经网络。
    自然语言处理是计算机科学和人工智能的一个分支，它帮助计算机理解、解释和生成人类语言。
    计算机视觉是人工智能的一个领域，它训练计算机解释和理解来自图像和视频的视觉世界。
    数据科学是一个跨学科领域，使用科学方法、过程、算法和系统从数据中提取知识和见解。
    大数据是指传统数据处理应用软件无法处理的大型复杂数据集。
    云计算是通过互联网提供计算服务，包括服务器、存储、数据库、网络、软件等。
    物联网是指通过互联网连接的物理设备网络，这些设备可以收集和共享数据。
    区块链是一种分布式账本技术，以安全、去中心化的方式记录交易。
    虚拟现实是计算机生成的模拟环境，可以探索和与虚拟世界交互。
    增强现实是将虚拟信息叠加到现实世界上的技术。
    量子计算是利用量子力学现象进行计算的技术。
    5G是第五代移动通信技术，提供更快的数据速度和更可靠的连接。
    网络安全是保护计算机系统和网络免受数字攻击的做法。
    """
    
    try:
        # 1. 初始化处理器
        print("1. 初始化文本处理器...")
        processor = TextProcessor()
        
        # 2. 提取关键词
        print("2. 提取关键词...")
        keywords = processor.extract_keywords(test_text, top_k=50)
        print(f"   提取到 {len(keywords)} 个关键词")
        
        # 3. 过滤关键词
        print("3. 过滤关键词...")
        keyword_filter = KeywordFilter()
        filtered_keywords = keyword_filter.filter_keywords(
            keywords,
            min_freq=1,
            min_len=2,
            max_len=10
        )
        print(f"   过滤后保留 {len(filtered_keywords)} 个关键词")
        
        # 显示前10个关键词
        print("   前10个关键词:")
        for i, (word, freq) in enumerate(filtered_keywords[:10], 1):
            print(f"     {i}. {word}: {freq}")
        
        # 4. 生成词云
        print("4. 生成词云...")
        wordcloud_gen = WordCloudGenerator()
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 生成默认风格词云
        wordcloud_path = os.path.join(output_dir, f"test_wordcloud_{timestamp}.png")
        
        success = wordcloud_gen.generate_and_save(
            filtered_keywords,
            wordcloud_path,
            background_color='white',
            colormap='viridis'
        )
        
        if success:
            print(f"   [成功] 词云已保存: {wordcloud_path}")
        else:
            print("   [失败] 词云生成失败")
        
        # 5. 导出Excel
        print("5. 导出Excel...")
        excel_exporter = ExcelExporter()
        excel_path = os.path.join(output_dir, f"test_keywords_{timestamp}.xlsx")
        
        excel_success = excel_exporter.export_keywords_to_excel(
            filtered_keywords,
            excel_path,
            include_chart=True,
            include_stats=True
        )
        
        if excel_success:
            print(f"   [成功] Excel已保存: {excel_path}")
        else:
            print("   [失败] Excel导出失败")
        
        # 6. 导出过滤详情
        print("6. 导出过滤详情...")
        filter_details_path = os.path.join(output_dir, f"test_filter_details_{timestamp}.xlsx")
        
        filter_success = excel_exporter.export_filter_details(
            keywords,
            filtered_keywords,
            filter_details_path
        )
        
        if filter_success:
            print(f"   [成功] 过滤详情已保存: {filter_details_path}")
        else:
            print("   [失败] 过滤详情导出失败")
        
        print("\n[成功] 所有核心功能测试完成!")
        
        # 显示输出文件
        print(f"\n输出文件位于: {output_dir}")
        for filename in os.listdir(output_dir):
            filepath = os.path.join(output_dir, filename)
            filesize = os.path.getsize(filepath)
            print(f"  - {filename} ({filesize} bytes)")
        
        return True
        
    except Exception as e:
        print(f"[失败] 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_statistics():
    """测试统计功能"""
    print("\n测试统计功能...")
    
    try:
        processor = TextProcessor()
        
        # 简单测试文本
        simple_text = "测试测试测试人工智能人工智能机器学习深度学习自然语言处理"
        
        keywords = processor.extract_keywords(simple_text, top_k=20)
        stats = processor.get_word_frequency_stats(keywords)
        
        print(f"统计信息:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        print("[成功] 统计功能测试完成!")
        return True
        
    except Exception as e:
        print(f"[失败] 统计功能测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("词云生成系统核心功能测试")
    print("=" * 50)
    
    # 测试核心功能
    success1 = test_core_functionality()
    
    # 测试统计功能
    success2 = test_statistics()
    
    print("\n" + "=" * 50)
    if success1 and success2:
        print("[成功] 所有测试通过!")
    else:
        print("[失败] 部分测试失败")
    print("=" * 50)

if __name__ == "__main__":
    main()