"""
词云生成系统测试脚本
用于验证系统功能
"""
import os
import sys
import logging
from main import WordCloudSystem

def test_system():
    """测试系统功能"""
    print("开始测试词云生成系统...")
    
    # 创建系统实例
    config = {
        'output_dir': 'test_output',
        'log_level': 'INFO'
    }
    
    system = WordCloudSystem(config)
    
    # 测试文本内容
    test_text = """
    人工智能是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。
    该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等。
    人工智能从诞生以来，理论和技术日益成熟，应用领域也不断扩大。
    可以设想，未来人工智能带来的科技产品，将会是人类智慧的"容器"。
    人工智能可以对人的意识、思维的信息过程的模拟。
    人工智能不是人的智能，但能像人那样思考、也可能超过人的智能。
    人工智能是一门极富挑战性的科学，从事这项工作的人必须懂得计算机知识，心理学和哲学。
    人工智能是包括十分广泛的科学，它由不同的领域组成，如机器学习，计算机视觉等等。
    总的说来，人工智能研究的一个主要目标是使机器能够胜任一些通常需要人类智能才能完成的复杂工作。
    """
    
    # 测试处理文本
    print("\n测试1: 处理文本内容")
    options = {
        'top_k': 50,
        'min_freq': 1,
        'min_len': 2,
        'max_len': 10,
        'styles': ['default', 'blue_theme'],
        'include_chart': True,
        'include_stats': True
    }
    
    result = system.process_text(test_text, options)
    
    if result['success']:
        print("✓ 文本处理成功")
        print(f"  - 原始关键词数量: {result['original_keywords_count']}")
        print(f"  - 过滤后关键词数量: {result['filtered_keywords_count']}")
        print(f"  - 词云文件: {result['wordcloud_file']}")
        print(f"  - Excel文件: {result['excel_file']}")
        print(f"  - 前5个关键词: {[word for word, freq in result['top_keywords'][:5]]}")
    else:
        print("✗ 文本处理失败")
        print(f"  - 错误信息: {result['error']}")
    
    # 测试处理URL（使用示例URL）
    print("\n测试2: 处理URL")
    test_url = "https://baike.baidu.com/item/%E4%BA%BA%E5%B7%A5%E6%99%BA%E8%83%BD/9180"
    
    result = system.process_url(test_url, options)
    
    if result['success']:
        print("✓ URL处理成功")
        print(f"  - 文章长度: {result['article_length']}")
        print(f"  - 原始关键词数量: {result['original_keywords_count']}")
        print(f"  - 过滤后关键词数量: {result['filtered_keywords_count']}")
        print(f"  - 生成的词云文件: {len(result['wordcloud_files'])}个")
        print(f"  - Excel文件: {result['excel_file']}")
    else:
        print("✗ URL处理失败")
        print(f"  - 错误信息: {result['error']}")
    
    # 检查输出文件
    print("\n测试3: 检查输出文件")
    output_dir = 'test_output'
    
    if os.path.exists(output_dir):
        files = os.listdir(output_dir)
        print(f"✓ 输出目录存在，包含 {len(files)} 个文件:")
        for file in files:
            file_path = os.path.join(output_dir, file)
            file_size = os.path.getsize(file_path)
            print(f"  - {file} ({file_size} bytes)")
    else:
        print("✗ 输出目录不存在")
    
    print("\n测试完成!")

if __name__ == "__main__":
    test_system()